generator client {
  provider = "prisma-client-js"
  output   = "../generated/prisma"
}

datasource db {
  provider = "mongodb"
  url      = env("DATABASE_URL")
}

enum Role {
  student
  recruiter
}

enum ApplicationStatus {
  pending
  accepted
  rejected
}

model User {
  id                        String        @id @default(auto()) @map("_id") @db.ObjectId
  fullname                  String
  email                     String        @unique
  phoneNumber               String
  password                  String
  role                      Role          @default(student)
  profileBio                String?
  profileSkills             String[]
  profileResume             String?
  profileResumeOriginalName String?
  profileComapnyId          String?       @db.ObjectId
  profilePhoto              String        @default("")
  createdAt                 DateTime      @default(now())
  updatedAt                 DateTime      @updatedAt
  Company                   Company[]
  Application               Application[]
  Favorite                  Favorite[]
}

model Job {
  id              String   @id @default(auto()) @map("_id") @db.ObjectId
  title           String
  description     String
  requirements    String[]
  salary          Float
  location        String
  jobType         String
  experienceLevel String
  position        Int

  companyId String  @db.ObjectId
  company   Company @relation(fields: [companyId], references: [id])

  createdAt   DateTime      @default(now())
  updatedAt   DateTime      @updatedAt
  Application Application[]
  Favorite    Favorite[]
}

model Company {
  id          String  @id @default(auto()) @map("_id") @db.ObjectId
  name        String
  description String
  location    String?
  website     String?
  logo        String?
  userId      String  @db.ObjectId
  user        User    @relation(fields: [userId], references: [id])

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  Job       Job[]
}

model Application {
  id          String            @id @default(auto()) @map("_id") @db.ObjectId
  jobId       String            @db.ObjectId
  job         Job               @relation(fields: [jobId], references: [id])
  applicantId String            @db.ObjectId
  applicant   User              @relation(fields: [applicantId], references: [id])
  status      ApplicationStatus @default(pending)
  createdAt   DateTime          @default(now())
  updatedAt   DateTime          @updatedAt
}

model Favorite {
  id        String   @id @default(auto()) @map("_id") @db.ObjectId
  userId    String   @db.ObjectId
  user      User     @relation(fields: [userId], references: [id])
  jobId     String   @db.ObjectId
  job       Job      @relation(fields: [jobId], references: [id])
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}
